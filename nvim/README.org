#+title: Readme

* Install Neovim
- Ubuntu
  Running the script
  #+begin_src shell
  bash install_neovim.sh
  #+end_src

* Config neovim
- Copying config for neovim by creating symbol
#+begin_src shell
ln -s ./nvim ~/.config/nvim
#+end_src shell
- Close neovim and reopen to activate the configuration

* LSP
- Location
#+begin_src shell
/home/<USER>/.local/share/nvim/mason/packages
#+end_src shell

* Reference
- https://martinlwx.github.io/en/config-neovim-from-scratch/
