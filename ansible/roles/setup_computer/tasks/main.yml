---
# tasks file for setup_computer

# Set shell configuration path globally for all tasks
- name: Detect user's shell
  shell: echo $SHELL
  register: user_shell
  become: false

- name: Print detected shell
  debug:
    msg: "Detected shell: {{ user_shell.stdout }}"

- name: Set shell config file path for zsh
  set_fact:
    shell_config_path: "{{ ansible_env.HOME }}/.zshrc"
  when: "'/zsh' in user_shell.stdout"

- name: Set shell config file path for bash (fallback)
  set_fact:
    shell_config_path: "{{ ansible_env.HOME }}/.bashrc"
  when: "'/zsh' not in user_shell.stdout"

- name: Print sehll configuration path
  debug:
    msg: "Shell configuration path: {{ shell_config_path }}"

- name: Install golang
  include_tasks: install_go.yml
