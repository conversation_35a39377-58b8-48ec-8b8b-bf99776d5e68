---
# tasks file for setup_computer

# Set shell configuration path globally for all tasks
- name: Get real user information
  shell: |
    if [ -n "$SUDO_USER" ]; then
      echo "$SUDO_USER"
    else
      whoami
    fi
  register: real_user
  become: false

- name: Get real user's home directory
  shell: "getent passwd {{ real_user.stdout }} | cut -d: -f6"
  register: real_user_home
  become: false

- name: Detect real user's shell
  shell: "getent passwd {{ real_user.stdout }} | cut -d: -f7"
  register: user_shell
  become: false

- name: Print detected information
  debug:
    msg:
      - "Real user: {{ real_user.stdout }}"
      - "Real user home: {{ real_user_home.stdout }}"
      - "Detected shell: {{ user_shell.stdout }}"

- name: Set shell config file path for zsh
  set_fact:
    shell_config_path: "{{ real_user_home.stdout }}/.zshrc"
  when: "'/zsh' in user_shell.stdout"

- name: Set shell config file path for bash (fallback)
  set_fact:
    shell_config_path: "{{ real_user_home.stdout }}/.bashrc"
  when: "'/zsh' not in user_shell.stdout"

- name: Print shell configuration path
  debug:
    msg: "Shell configuration path: {{ shell_config_path }}"

- name: Install golang
  include_tasks: install_go.yml
