- name: Check if Go is already installed
  stat:
    path: "{{ go_bin_path }}"
  register: go_stat

- block:
    - name: Download Go archive
      get_url:
        url: "{{ go_url }}"
        dest: "/tmp/{{ go_archive }}"
        mode: '0644'
        
    - name: Remove existing Go installation (if any)
      file:
        path: "{{ go_install_dir }}/go"
        state: absent

    - name: Extract Go archive
      unarchive:
        src: "/tmp/{{ go_archive }}"
        dest: "{{ go_install_dir }}"
        remote_src: yes

    - name: Detect user's shell
      shell: echo $SHELL
      register: user_shell
      become: false

    - name: Set shell config file path
      set_fact:
        shell_config_path: "{{ ansible_env.HOME }}/.zshrc"
      when: "'/zsh' in user_shell.stdout"

    - name: Set shell config file path (fallback to bash)
      set_fact:
        shell_config_path: "{{ ansible_env.HOME }}/.bashrc"
      when: "'/zsh' not in user_shell.stdout"

    - name: Ensure /usr/local/go/bin is in the PATH (user shell config)
      lineinfile:
        path: "{{ shell_config_path }}"
        line: 'export PATH=$PATH:/usr/local/go/bin'
        create: yes
        backup: yes
      become: false

    - name: Also create system-wide profile for compatibility
      lineinfile:
        path: /etc/profile.d/go.sh
        line: 'export PATH=$PATH:/usr/local/go/bin'
        create: yes
        mode: '0755'

    - name: Verify Go installation
      shell: "source {{ shell_config_path }} && go version"
      register: go_version_check
      become: false
      ignore_errors: true

    - name: Display Go version
      debug:
        msg: "Go installed successfully: {{ go_version_check.stdout }}"
      when: go_version_check.rc == 0

  when: not go_stat.stat.exists