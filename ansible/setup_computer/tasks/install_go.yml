- name: Check if Go is already installed
  stat:
    path: "{{ go_bin_path }}"
  register: go_stat

- block:
    - name: Download Go archive
      get_url:
        url: "{{ go_url }}"
        dest: "/tmp/{{ go_archive }}"
        mode: '0644'
        
    - name: Remove existing Go installation (if any)
      file:
        path: "{{ go_install_dir }}/go"
        state: absent

    - name: Extract Go archive
      unarchive:
        src: "/tmp/{{ go_archive }}"
        dest: "{{ go_install_dir }}"
        remote_src: yes

    - name: Ensure /usr/local/go/bin is in the PATH
      lineinfile:
        path: /etc/profile.d/go.sh
        line: 'export PATH=$PATH:/usr/local/go/bin'
        create: yes
        mode: '0755'

  when: not go_stat.stat.exists